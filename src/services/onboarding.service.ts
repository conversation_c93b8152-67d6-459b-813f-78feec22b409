import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {OrganizationRepository} from '../repositories';
import { getURL } from '../utils';
const fetch = require('node-fetch');

const ONBOARD_URL = process.env.ONBOARDING_AWS_URL || 'ojpurdnz1b.execute-api.us-east-1.amazonaws.com';

@injectable({scope: BindingScope.TRANSIENT})
export class OnboardingService {
  constructor(
    @repository(OrganizationRepository)
    private organizationRepository: OrganizationRepository,
  ) {}

  async onboardBegin(body: any, orgId: number) {
	if (!body.externalDomain) {
      await this.organizationRepository.findOne({
        where: {
          id: orgId
        }
      }).then((org) => {
        body.externalDomain = org?.externalDomain;
      });
    }

	if (!body.externalDomain) {
      return {
        statusCode: 400,
        body: {
          error: 'No external domain provided'
        }
      }
    }

    const url = body.externalDomain.startsWith('http')
      ? body.externalDomain
      : `https://${body.externalDomain}`;

    // Save orgType if provided
    if (body.orgType) {
      await this.organizationRepository.updateById(orgId, {
        orgType: body.orgType
      });
    }

    let payload = {
      organization_id: orgId,
      url
    }
    let response: any;
    try {
      const url = `/prod/onboard`;
      const signedRequest = getURL(url, 'POST', payload, ONBOARD_URL);
      response = await fetch(`https://${ONBOARD_URL}${url}`, signedRequest);
    } catch (err) {
      console.log("Error: " + JSON.stringify(err));
      throw new Error("Error: " + JSON.stringify(err));
    }

    const data: any = await response.json();

    return {
      statusCode: 200,
      body: {
        orgId: orgId,
        externalDomain: body.externalDomain,
        data: data
      }
    }
  }
}
